'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  Cog6ToothIcon,
  ComputerDesktopIcon,
  CloudIcon,
  CpuChipIcon,
  ChatBubbleLeftRightIcon,
  BookOpenIcon,
  CircleStackIcon,
  CommandLineIcon,
  InformationCircleIcon,
  PlusIcon,
  TrashIcon,
  CheckIcon,
  XMarkIcon,
  ExclamationTriangleIcon,
  EyeIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline';
import useConfigStore from '@/stores/useConfigStore';

interface ConfigurationTabContentProps {
    tab: any;
}

// 基础模型元数据接口
interface ModelMeta {
  id: string;
  name: string;
  group: string;
  providerId: string;
  isCustom: boolean;
  contextLength?: number;
  maxTokens?: number;
  vision?: boolean;
  functionCall?: boolean;
  reasoning?: boolean;
  description?: string;
  enabled?: boolean;
}

// LLM 提供商接口
interface LLMProvider {
  id: string;
  name: string;
  apiType: string;
  apiKey: string;
  baseUrl: string;
  enable: boolean;
  websites?: {
    official?: string;
    apiKey?: string;
    docs?: string;
    models?: string;
    defaultBaseUrl?: string;
  };
}

// 提供商验证结果接口
interface ProviderCheckResult {
  isOk: boolean;
  errorMsg: string | null;
}

// 基础 LLM 提供商抽象类接口
interface BaseLLMProviderInterface {
  provider: LLMProvider;
  models: ModelMeta[];
  customModels: ModelMeta[];
  isInitialized: boolean;

  // 核心方法
  fetchModels(): Promise<ModelMeta[]>;
  check(): Promise<ProviderCheckResult>;
  addCustomModel(model: Omit<ModelMeta, 'providerId' | 'isCustom' | 'group'>): ModelMeta;
  removeCustomModel(modelId: string): boolean;
  updateCustomModel(modelId: string, updates: Partial<ModelMeta>): boolean;
  getCustomModels(): ModelMeta[];
  getModels(): ModelMeta[];
}

// 默认提供商配置数据
const DEFAULT_PROVIDERS: LLMProvider[] = [
  {
    id: 'openai',
    name: 'OpenAI',
    apiType: 'openai',
    apiKey: '',
    baseUrl: 'https://api.openai.com/v1',
    enable: false,
    websites: {
      official: 'https://openai.com/',
      apiKey: 'https://platform.openai.com/api-keys',
      docs: 'https://platform.openai.com/docs',
      models: 'https://platform.openai.com/docs/models',
      defaultBaseUrl: 'https://api.openai.com/v1'
    }
  },
  {
    id: 'deepseek',
    name: 'Deepseek',
    apiType: 'deepseek',
    apiKey: '',
    baseUrl: 'https://api.deepseek.com/v1',
    enable: false,
    websites: {
      official: 'https://deepseek.com/',
      apiKey: 'https://platform.deepseek.com/api_keys',
      docs: 'https://platform.deepseek.com/api-docs/',
      models: 'https://platform.deepseek.com/api-docs/',
      defaultBaseUrl: 'https://api.deepseek.com/v1'
    }
  },
  {
    id: 'gemini',
    name: 'Google Gemini',
    apiType: 'gemini',
    apiKey: '',
    baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
    enable: false,
    websites: {
      official: 'https://ai.google.dev/',
      apiKey: 'https://makersuite.google.com/app/apikey',
      docs: 'https://ai.google.dev/docs',
      models: 'https://ai.google.dev/models',
      defaultBaseUrl: 'https://generativelanguage.googleapis.com/v1beta'
    }
  },
  {
    id: 'anthropic',
    name: 'Anthropic Claude',
    apiType: 'anthropic',
    apiKey: '',
    baseUrl: 'https://api.anthropic.com',
    enable: false,
    websites: {
      official: 'https://anthropic.com/',
      apiKey: 'https://console.anthropic.com/',
      docs: 'https://docs.anthropic.com/',
      models: 'https://docs.anthropic.com/claude/docs/models-overview',
      defaultBaseUrl: 'https://api.anthropic.com'
    }
  },
  {
    id: 'ollama',
    name: 'Ollama',
    apiType: 'ollama',
    apiKey: '',
    baseUrl: 'http://localhost:11434',
    enable: false,
    websites: {
      official: 'https://ollama.com/',
      apiKey: '',
      docs: 'https://github.com/ollama/ollama/tree/main/docs',
      models: 'https://ollama.com/library',
      defaultBaseUrl: 'http://localhost:11434'
    }
  },
  {
    id: 'siliconflow',
    name: '硅基流动',
    apiType: 'openai-compatible',
    apiKey: '',
    baseUrl: 'https://api.siliconflow.cn/v1',
    enable: false,
    websites: {
      official: 'https://siliconflow.cn/',
      apiKey: 'https://cloud.siliconflow.cn/account/ak',
      docs: 'https://docs.siliconflow.cn/',
      models: 'https://docs.siliconflow.cn/docs/model-names',
      defaultBaseUrl: 'https://api.siliconflow.cn/v1'
    }
  }
];

// 配置菜单项类型
interface ConfigMenuItem {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
}

// 配置菜单项
const configMenuItems: ConfigMenuItem[] = [
  { id: 'general', label: '通用设置', icon: Cog6ToothIcon },
  { id: 'display', label: '显示设置', icon: ComputerDesktopIcon },
  { id: 'providers', label: '服务商设置', icon: CloudIcon },
  { id: 'mcp', label: 'MCP设置', icon: CpuChipIcon },
  { id: 'prompts', label: 'Prompt管理', icon: ChatBubbleLeftRightIcon },
  { id: 'knowledge', label: '知识库', icon: BookOpenIcon },
  { id: 'data', label: '数据设置', icon: CircleStackIcon },
  { id: 'shortcuts', label: '快捷键', icon: CommandLineIcon },
  { id: 'about', label: '关于', icon: InformationCircleIcon },
];

// 基础 LLM 提供商抽象类实现
abstract class BaseLLMProvider implements BaseLLMProviderInterface {
  protected static readonly MAX_TOOL_CALLS = 50;

  public provider: LLMProvider;
  public models: ModelMeta[] = [];
  public customModels: ModelMeta[] = [];
  public isInitialized: boolean = false;

  constructor(provider: LLMProvider) {
    this.provider = provider;
  }

  // 抽象方法，由具体提供商实现
  abstract fetchProviderModels(): Promise<ModelMeta[]>;
  abstract check(): Promise<ProviderCheckResult>;

  // 获取模型列表
  async fetchModels(): Promise<ModelMeta[]> {
    try {
      const models = await this.fetchProviderModels();
      console.log('Fetched models:', models?.length, this.provider.id);
      this.models = models;
      return models;
    } catch (e) {
      console.error('Failed to fetch models:', e);
      if (!this.models) {
        this.models = [];
      }
      return [];
    }
  }

  // 获取所有模型（包括自定义模型）
  getModels(): ModelMeta[] {
    return [...this.models, ...this.customModels];
  }

  // 添加自定义模型
  addCustomModel(model: Omit<ModelMeta, 'providerId' | 'isCustom' | 'group'>): ModelMeta {
    const newModel: ModelMeta = {
      ...model,
      providerId: this.provider.id,
      isCustom: true,
      group: 'default'
    };

    // 检查是否已存在相同ID的自定义模型
    const existingIndex = this.customModels.findIndex((m) => m.id === newModel.id);
    if (existingIndex !== -1) {
      this.customModels[existingIndex] = newModel;
    } else {
      this.customModels.push(newModel);
    }

    return newModel;
  }

  // 删除自定义模型
  removeCustomModel(modelId: string): boolean {
    const index = this.customModels.findIndex((model) => model.id === modelId);
    if (index !== -1) {
      this.customModels.splice(index, 1);
      return true;
    }
    return false;
  }

  // 更新自定义模型
  updateCustomModel(modelId: string, updates: Partial<ModelMeta>): boolean {
    const model = this.customModels.find((m) => m.id === modelId);
    if (model) {
      Object.assign(model, updates);
      return true;
    }
    return false;
  }

  // 获取所有自定义模型
  getCustomModels(): ModelMeta[] {
    return this.customModels;
  }
}

// OpenAI 兼容提供商实现
class OpenAICompatibleProvider extends BaseLLMProvider {
  async fetchProviderModels(): Promise<ModelMeta[]> {
    // 模拟 OpenAI API 调用获取模型列表
    try {
      const response = await fetch(`${this.provider.baseUrl}/models`, {
        headers: {
          'Authorization': `Bearer ${this.provider.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data.data?.map((model: any) => ({
        id: model.id,
        name: model.id,
        group: 'default',
        providerId: this.provider.id,
        isCustom: false,
        contextLength: 4096,
        maxTokens: 2048
      })) || [];
    } catch (error) {
      console.error(`Failed to fetch models for ${this.provider.name}:`, error);
      return [];
    }
  }

  async check(): Promise<ProviderCheckResult> {
    try {
      const response = await fetch(`${this.provider.baseUrl}/models`, {
        headers: {
          'Authorization': `Bearer ${this.provider.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        return {
          isOk: false,
          errorMsg: `HTTP ${response.status}: ${response.statusText}`
        };
      }

      return { isOk: true, errorMsg: null };
    } catch (error) {
      return {
        isOk: false,
        errorMsg: error instanceof Error ? error.message : String(error)
      };
    }
  }
}

// 获取服务商图标
const getProviderIcon = (providerId: string): string => {
  const icons: Record<string, string> = {
    ollama: 'O',
    deepseek: 'D',
    qiniu: '七',
    siliconflow: 'S',
    doubao: '豆',
    minimax: 'M',
    fireworks: 'F',
    paimeng: 'P',
    'openai-responses': 'OR',
    openai: 'AI',
    gemini: 'G',
    anthropic: 'A',
    'github-models': 'GH',
  };
  return icons[providerId] || providerId.charAt(0).toUpperCase();
};

// 具体提供商实现类
class OpenAIProvider extends OpenAICompatibleProvider {
  constructor(provider: LLMProvider) {
    super(provider);
  }
}

class DeepSeekProvider extends OpenAICompatibleProvider {
  constructor(provider: LLMProvider) {
    super(provider);
  }
}

class GeminiProvider extends BaseLLMProvider {
  // Gemini 静态模型列表
  private static readonly GEMINI_MODELS: ModelMeta[] = [
    {
      id: 'gemini-2.0-flash',
      name: 'Gemini 2.0 Flash',
      group: 'default',
      providerId: 'gemini',
      isCustom: false,
      contextLength: 1048576,
      maxTokens: 8192,
      vision: true,
      functionCall: true,
      reasoning: false,
      description: 'Gemini 2.0 Flash 模型'
    },
    {
      id: 'gemini-1.5-pro',
      name: 'Gemini 1.5 Pro',
      group: 'default',
      providerId: 'gemini',
      isCustom: false,
      contextLength: 2097152,
      maxTokens: 8192,
      vision: true,
      functionCall: true,
      reasoning: false,
      description: 'Gemini 1.5 Pro 模型（更强大、支持多模态）'
    },
    {
      id: 'gemini-1.5-flash',
      name: 'Gemini 1.5 Flash',
      group: 'default',
      providerId: 'gemini',
      isCustom: false,
      contextLength: 1048576,
      maxTokens: 8192,
      vision: true,
      functionCall: true,
      reasoning: false,
      description: 'Gemini 1.5 Flash 模型（更快速、性价比更高）'
    }
  ];

  async fetchProviderModels(): Promise<ModelMeta[]> {
    // Gemini 使用静态模型列表
    return GeminiProvider.GEMINI_MODELS.map((model) => ({
      ...model,
      providerId: this.provider.id
    }));
  }

  async check(): Promise<ProviderCheckResult> {
    try {
      if (!this.provider.apiKey) {
        return { isOk: false, errorMsg: '缺少API密钥' };
      }

      // 简单的 API 测试调用
      const response = await fetch(
        `${this.provider.baseUrl}/models/gemini-1.5-flash:generateContent?key=${this.provider.apiKey}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            contents: [{ parts: [{ text: 'Hello' }] }]
          })
        }
      );

      return { isOk: response.ok, errorMsg: response.ok ? null : `HTTP ${response.status}` };
    } catch (error) {
      return {
        isOk: false,
        errorMsg: error instanceof Error ? error.message : String(error)
      };
    }
  }
}

class AnthropicProvider extends BaseLLMProvider {
  // Anthropic 静态模型列表
  private static readonly ANTHROPIC_MODELS: ModelMeta[] = [
    {
      id: 'claude-3-5-sonnet-20241022',
      name: 'Claude 3.5 Sonnet',
      group: 'default',
      providerId: 'anthropic',
      isCustom: false,
      contextLength: 200000,
      maxTokens: 8192,
      vision: true,
      functionCall: true,
      reasoning: false,
      description: 'Claude 3.5 Sonnet 模型'
    },
    {
      id: 'claude-3-5-haiku-20241022',
      name: 'Claude 3.5 Haiku',
      group: 'default',
      providerId: 'anthropic',
      isCustom: false,
      contextLength: 200000,
      maxTokens: 8192,
      vision: true,
      functionCall: true,
      reasoning: false,
      description: 'Claude 3.5 Haiku 模型'
    },
    {
      id: 'claude-3-opus-20240229',
      name: 'Claude 3 Opus',
      group: 'default',
      providerId: 'anthropic',
      isCustom: false,
      contextLength: 200000,
      maxTokens: 4096,
      vision: true,
      functionCall: true,
      reasoning: false,
      description: 'Claude 3 Opus 模型'
    }
  ];

  async fetchProviderModels(): Promise<ModelMeta[]> {
    // Anthropic 使用静态模型列表
    return AnthropicProvider.ANTHROPIC_MODELS.map((model) => ({
      ...model,
      providerId: this.provider.id
    }));
  }

  async check(): Promise<ProviderCheckResult> {
    try {
      if (!this.provider.apiKey) {
        return { isOk: false, errorMsg: '缺少API密钥' };
      }

      // 简单的 API 测试调用
      const response = await fetch(`${this.provider.baseUrl}/v1/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': this.provider.apiKey,
          'anthropic-version': '2023-06-01'
        },
        body: JSON.stringify({
          model: 'claude-3-5-haiku-20241022',
          max_tokens: 10,
          messages: [{ role: 'user', content: 'Hello' }]
        })
      });

      return { isOk: response.ok, errorMsg: response.ok ? null : `HTTP ${response.status}` };
    } catch (error) {
      return {
        isOk: false,
        errorMsg: error instanceof Error ? error.message : String(error)
      };
    }
  }
}

class OllamaProvider extends BaseLLMProvider {
  async fetchProviderModels(): Promise<ModelMeta[]> {
    try {
      const response = await fetch(`${this.provider.baseUrl}/api/tags`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data.models?.map((model: any) => ({
        id: model.name,
        name: model.name,
        group: 'default',
        providerId: this.provider.id,
        isCustom: false,
        contextLength: 4096,
        maxTokens: 2048,
        description: `Ollama 模型: ${model.name}`
      })) || [];
    } catch (error) {
      console.error(`Failed to fetch Ollama models:`, error);
      return [];
    }
  }

  async check(): Promise<ProviderCheckResult> {
    try {
      const response = await fetch(`${this.provider.baseUrl}/api/tags`);
      return { isOk: response.ok, errorMsg: response.ok ? null : `HTTP ${response.status}` };
    } catch (error) {
      return {
        isOk: false,
        errorMsg: error instanceof Error ? error.message : String(error)
      };
    }
  }
}

// 提供商工厂函数
function createProviderInstance(provider: LLMProvider): BaseLLMProvider {
  switch (provider.apiType) {
    case 'openai':
      return new OpenAIProvider(provider);
    case 'deepseek':
      return new DeepSeekProvider(provider);
    case 'gemini':
      return new GeminiProvider(provider);
    case 'anthropic':
      return new AnthropicProvider(provider);
    case 'ollama':
      return new OllamaProvider(provider);
    case 'openai-compatible':
      return new OpenAICompatibleProvider(provider);
    default:
      console.warn(`Unknown provider type: ${provider.apiType}`);
      return new OpenAICompatibleProvider(provider);
  }
}

// 获取服务商图标样式
const getProviderIconStyle = (providerId: string): string => {
  const styles: Record<string, string> = {
    ollama: 'bg-gray-600',
    deepseek: 'bg-blue-600',
    qiniu: 'bg-green-600',
    siliconflow: 'bg-purple-600',
    doubao: 'bg-orange-600',
    minimax: 'bg-red-600',
    fireworks: 'bg-yellow-600',
    paimeng: 'bg-pink-600',
    'openai-responses': 'bg-teal-600',
    openai: 'bg-green-700',
    gemini: 'bg-blue-700',
    anthropic: 'bg-indigo-600',
    'github-models': 'bg-gray-800',
  };
  return styles[providerId] || 'bg-gray-500';
};

const ConfigurationTabContent: React.FC<ConfigurationTabContentProps> = () => {
  const [activeMenuItem, setActiveMenuItem] = useState('providers');

  // 增强的提供商管理状态
  const [providers, setProviders] = useState<LLMProvider[]>(DEFAULT_PROVIDERS);
  const [selectedProviderId, setSelectedProviderId] = useState<string | null>(null);
  const [providerInstances, setProviderInstances] = useState<Map<string, BaseLLMProvider>>(new Map());
  const [providerModels, setProviderModels] = useState<Map<string, ModelMeta[]>>(new Map());
  const [isLoadingModels, setIsLoadingModels] = useState<Set<string>>(new Set());
  const [validationResults, setValidationResults] = useState<Map<string, ProviderCheckResult>>(new Map());

  // 初始化提供商实例
  useEffect(() => {
    const instances = new Map<string, BaseLLMProvider>();
    providers.forEach(provider => {
      if (provider.enable) {
        const instance = createProviderInstance(provider);
        instances.set(provider.id, instance);
      }
    });
    setProviderInstances(instances);
  }, [providers]);

  // 获取提供商模型列表
  const fetchProviderModels = useCallback(async (providerId: string) => {
    const instance = providerInstances.get(providerId);
    if (!instance) return;

    setIsLoadingModels(prev => new Set(prev).add(providerId));
    try {
      const models = await instance.fetchModels();
      setProviderModels(prev => new Map(prev).set(providerId, models));
    } catch (error) {
      console.error(`Failed to fetch models for ${providerId}:`, error);
    } finally {
      setIsLoadingModels(prev => {
        const newSet = new Set(prev);
        newSet.delete(providerId);
        return newSet;
      });
    }
  }, [providerInstances]);

  // 验证提供商 API
  const validateProvider = useCallback(async (providerId: string) => {
    const instance = providerInstances.get(providerId);
    if (!instance) return;

    try {
      const result = await instance.check();
      setValidationResults(prev => new Map(prev).set(providerId, result));
      return result;
    } catch (error) {
      const errorResult = {
        isOk: false,
        errorMsg: error instanceof Error ? error.message : String(error)
      };
      setValidationResults(prev => new Map(prev).set(providerId, errorResult));
      return errorResult;
    }
  }, [providerInstances]);

  // 更新提供商配置
  const updateProvider = useCallback((providerId: string, updates: Partial<LLMProvider>) => {
    setProviders(prev => prev.map(provider =>
      provider.id === providerId ? { ...provider, ...updates } : provider
    ));
  }, []);

  // 切换提供商启用状态
  const toggleProvider = useCallback((providerId: string) => {
    setProviders(prev => prev.map(provider =>
      provider.id === providerId ? { ...provider, enable: !provider.enable } : provider
    ));
  }, []);

  // 添加自定义提供商
  const addCustomProvider = useCallback((provider: Omit<LLMProvider, 'enable'>) => {
    const newProvider: LLMProvider = { ...provider, enable: false };
    setProviders(prev => [...prev, newProvider]);
  }, []);

  // 删除提供商
  const removeProvider = useCallback((providerId: string) => {
    setProviders(prev => prev.filter(provider => provider.id !== providerId));
    setProviderInstances(prev => {
      const newMap = new Map(prev);
      newMap.delete(providerId);
      return newMap;
    });
    setProviderModels(prev => {
      const newMap = new Map(prev);
      newMap.delete(providerId);
      return newMap;
    });
    setValidationResults(prev => {
      const newMap = new Map(prev);
      newMap.delete(providerId);
      return newMap;
    });
  }, []);

  const renderConfigContent = () => {
    switch (activeMenuItem) {
      case 'providers':
        return (
          <EnhancedProviderSettings
            providers={providers}
            selectedProviderId={selectedProviderId}
            setSelectedProviderId={setSelectedProviderId}
            providerModels={providerModels}
            isLoadingModels={isLoadingModels}
            validationResults={validationResults}
            onUpdateProvider={updateProvider}
            onToggleProvider={toggleProvider}
            onFetchModels={fetchProviderModels}
            onValidateProvider={validateProvider}
            onAddCustomProvider={addCustomProvider}
            onRemoveProvider={removeProvider}
          />
        );
      case 'general':
        return <div className="p-6">通用设置内容</div>;
      case 'display':
        return <div className="p-6">显示设置内容</div>;
      case 'mcp':
        return <div className="p-6">MCP设置内容</div>;
      case 'prompts':
        return <div className="p-6">Prompt管理内容</div>;
      case 'knowledge':
        return <div className="p-6">知识库内容</div>;
      case 'data':
        return <div className="p-6">数据设置内容</div>;
      case 'shortcuts':
        return <div className="p-6">快捷键内容</div>;
      case 'about':
        return <div className="p-6">关于内容</div>;
      default:
        return <div className="p-6">请选择配置项</div>;
    }
  };

  return (
    <div className="flex h-full bg-white dark:bg-gray-900">
      {/* 左侧配置菜单 */}
      <div className="w-64 bg-gray-50 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex-shrink-0">
        <div className="p-4">
          <nav className="space-y-1">
            {configMenuItems.map((item) => {
              const Icon = item.icon;
              const isActive = activeMenuItem === item.id;

              return (
                <button
                  key={item.id}
                  onClick={() => setActiveMenuItem(item.id)}
                  className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                    isActive
                      ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }`}
                >
                  <Icon className="mr-3 h-5 w-5" />
                  {item.label}
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* 右侧配置内容 */}
      <div className="flex-1 overflow-auto">
        {renderConfigContent()}
      </div>
    </div>
  );
};

// 增强的提供商设置组件接口
interface EnhancedProviderSettingsProps {
  providers: LLMProvider[];
  selectedProviderId: string | null;
  setSelectedProviderId: (id: string | null) => void;
  providerModels: Map<string, ModelMeta[]>;
  isLoadingModels: Set<string>;
  validationResults: Map<string, ProviderCheckResult>;
  onUpdateProvider: (providerId: string, updates: Partial<LLMProvider>) => void;
  onToggleProvider: (providerId: string) => void;
  onFetchModels: (providerId: string) => Promise<void>;
  onValidateProvider: (providerId: string) => Promise<ProviderCheckResult | undefined>;
  onAddCustomProvider: (provider: Omit<LLMProvider, 'enable'>) => void;
  onRemoveProvider: (providerId: string) => void;
}

// 增强的服务商设置组件
const EnhancedProviderSettings: React.FC<EnhancedProviderSettingsProps> = ({
  providers,
  selectedProviderId,
  setSelectedProviderId,
  providerModels,
  isLoadingModels,
  validationResults,
  onUpdateProvider,
  onToggleProvider,
  onFetchModels,
  onValidateProvider,
  onAddCustomProvider,
  onRemoveProvider
}) => {
  return (
    <div className="flex h-full">
      <EnhancedProviderList
        providers={providers}
        selectedProviderId={selectedProviderId}
        setSelectedProviderId={setSelectedProviderId}
        validationResults={validationResults}
        onToggleProvider={onToggleProvider}
        onAddCustomProvider={onAddCustomProvider}
        onRemoveProvider={onRemoveProvider}
      />
      <EnhancedProviderDetail
        providers={providers}
        selectedProviderId={selectedProviderId}
        providerModels={providerModels}
        isLoadingModels={isLoadingModels}
        validationResults={validationResults}
        onUpdateProvider={onUpdateProvider}
        onFetchModels={onFetchModels}
        onValidateProvider={onValidateProvider}
      />
    </div>
  );
};

// 原有的提供商详情组件（保留用于兼容）
const ProviderDetail: React.FC = () => {
  const {
    providers,
    selectedProviderId,
    updateProviderApiKey,
    updateProviderApiUrl,
    validateApiKey,
    toggleProviderModel,
    enableAllModels,
    disableAllModels
  } = useConfigStore();

  const [isValidating, setIsValidating] = useState(false);
  const [showApiKey, setShowApiKey] = useState(false);

  const selectedProvider = providers.find(p => p.id === selectedProviderId);

  if (!selectedProvider) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <p className="text-gray-500 dark:text-gray-400">请选择一个服务商</p>
      </div>
    );
  }

  const handleValidateApiKey = async () => {
    if (!selectedProvider.apiKey.trim()) {
      alert('请先输入API Key');
      return;
    }

    setIsValidating(true);
    try {
      const isValid = await validateApiKey(selectedProvider.id);
      if (isValid) {
        alert('API Key验证成功！');
      } else {
        alert('API Key验证失败，请检查密钥是否正确');
      }
    } catch (error) {
      alert('验证过程中出现错误');
    } finally {
      setIsValidating(false);
    }
  };

  const handleGetApiKey = () => {
    // 根据不同服务商打开对应的获取API Key页面
    const urls: Record<string, string> = {
      openai: 'https://platform.openai.com/api-keys',
      deepseek: 'https://platform.deepseek.com/api-keys',
      gemini: 'https://makersuite.google.com/app/apikey',
      // 可以添加更多服务商的URL
    };

    const url = urls[selectedProvider.id];
    if (url) {
      window.open(url, '_blank');
    } else {
      alert('请访问该服务商官网获取API Key');
    }
  };

  return (
    <div className="flex-1 bg-white dark:bg-gray-900 overflow-auto">
      <div className="p-6">
        <div className="max-w-2xl">
          {/* 服务商标题 */}
          <div className="flex items-center space-x-3 mb-6">
            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${getProviderIconStyle(selectedProvider.id)}`}>
              <span className="text-sm font-medium text-white">
                {getProviderIcon(selectedProvider.id)}
              </span>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              {selectedProvider.name}
            </h2>
          </div>

          {/* API URL配置 */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              API URL
            </label>
            <input
              type="text"
              value={selectedProvider.apiUrl || ''}
              onChange={(e) => updateProviderApiUrl(selectedProvider.id, e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
              placeholder="请输入API URL"
            />
          </div>

          {/* API Key配置 */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              API Key
            </label>
            <div className="relative">
              <input
                type={showApiKey ? "text" : "password"}
                value={selectedProvider.apiKey}
                onChange={(e) => updateProviderApiKey(selectedProvider.id, e.target.value)}
                className="w-full px-3 py-2 pr-20 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
                placeholder="请输入API Key"
              />
              <button
                type="button"
                onClick={() => setShowApiKey(!showApiKey)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
              >
                {showApiKey ? '隐藏' : '显示'}
              </button>
            </div>

            {/* API Key操作按钮 */}
            <div className="mt-3 flex space-x-3">
              <button
                onClick={handleValidateApiKey}
                disabled={isValidating || !selectedProvider.apiKey.trim()}
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isValidating ? '验证中...' : '验证密钥'}
              </button>

              <button
                onClick={handleGetApiKey}
                className="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                如何获取
              </button>
            </div>
          </div>

          {/* 模型列表 */}
          <ModelList provider={selectedProvider} />
        </div>
      </div>
    </div>
  );
};

// 服务商设置组件（保留原有的，用于兼容）
const ProviderSettings: React.FC = () => {
  return (
    <div className="flex h-full">
      <ProviderList />
      <ProviderDetail />
    </div>
  );
};

// 增强的提供商列表组件接口
interface EnhancedProviderListProps {
  providers: LLMProvider[];
  selectedProviderId: string | null;
  setSelectedProviderId: (id: string | null) => void;
  validationResults: Map<string, ProviderCheckResult>;
  onToggleProvider: (providerId: string) => void;
  onAddCustomProvider: (provider: Omit<LLMProvider, 'enable'>) => void;
  onRemoveProvider: (providerId: string) => void;
}

// 增强的提供商列表组件
const EnhancedProviderList: React.FC<EnhancedProviderListProps> = ({
  providers,
  selectedProviderId,
  setSelectedProviderId,
  validationResults,
  onToggleProvider,
  onAddCustomProvider,
  onRemoveProvider
}) => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [newProvider, setNewProvider] = useState({
    id: '',
    name: '',
    apiType: 'openai-compatible',
    apiKey: '',
    baseUrl: ''
  });

  const handleAddProvider = () => {
    if (newProvider.id && newProvider.name && newProvider.baseUrl) {
      onAddCustomProvider({
        ...newProvider,
        websites: {
          defaultBaseUrl: newProvider.baseUrl
        }
      });
      setNewProvider({
        id: '',
        name: '',
        apiType: 'openai-compatible',
        apiKey: '',
        baseUrl: ''
      });
      setShowAddForm(false);
    }
  };

  return (
    <div className="w-80 border-r border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 flex-shrink-0 flex flex-col">
      {/* 标题和添加按钮 */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">服务商列表</h3>
          <button
            onClick={() => setShowAddForm(!showAddForm)}
            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800"
            title="添加自定义服务商"
          >
            <PlusIcon className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* 添加提供商表单 */}
      {showAddForm && (
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <div className="space-y-3">
            <input
              type="text"
              placeholder="提供商ID"
              value={newProvider.id}
              onChange={(e) => setNewProvider(prev => ({ ...prev, id: e.target.value }))}
              className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
            <input
              type="text"
              placeholder="提供商名称"
              value={newProvider.name}
              onChange={(e) => setNewProvider(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
            <select
              value={newProvider.apiType}
              onChange={(e) => setNewProvider(prev => ({ ...prev, apiType: e.target.value }))}
              className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="openai-compatible">OpenAI 兼容</option>
              <option value="openai">OpenAI</option>
              <option value="anthropic">Anthropic</option>
              <option value="gemini">Gemini</option>
              <option value="ollama">Ollama</option>
            </select>
            <input
              type="text"
              placeholder="API URL"
              value={newProvider.baseUrl}
              onChange={(e) => setNewProvider(prev => ({ ...prev, baseUrl: e.target.value }))}
              className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
            <div className="flex space-x-2">
              <button
                onClick={handleAddProvider}
                className="flex-1 px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                添加
              </button>
              <button
                onClick={() => setShowAddForm(false)}
                className="flex-1 px-3 py-2 text-sm bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 提供商列表 */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4">
          <div className="space-y-1">
            {providers.map((provider) => {
              const validationResult = validationResults.get(provider.id);
              const isSelected = selectedProviderId === provider.id;

              return (
                <div
                  key={provider.id}
                  className={`flex items-center justify-between py-2 px-3 rounded-lg cursor-pointer transition-colors ${
                    isSelected
                      ? 'bg-blue-50 dark:bg-blue-900/20'
                      : 'hover:bg-gray-50 dark:hover:bg-gray-800'
                  }`}
                  onClick={() => setSelectedProviderId(provider.id)}
                >
                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    <div className="flex-shrink-0">
                      {/* 服务商图标 */}
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${getProviderIconStyle(provider.id)}`}>
                        <span className="text-xs font-medium text-white">
                          {getProviderIcon(provider.id)}
                        </span>
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {provider.name}
                        </p>
                        {/* 验证状态指示器 */}
                        {validationResult && (
                          <div className={`w-2 h-2 rounded-full ${
                            validationResult.isOk ? 'bg-green-500' : 'bg-red-500'
                          }`} title={validationResult.isOk ? 'API 验证成功' : validationResult.errorMsg || 'API 验证失败'} />
                        )}
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                        {provider.apiType}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    {/* 删除按钮（仅自定义提供商） */}
                    {!DEFAULT_PROVIDERS.find(p => p.id === provider.id) && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onRemoveProvider(provider.id);
                        }}
                        className="p-1 text-gray-400 hover:text-red-600 dark:hover:text-red-400"
                        title="删除提供商"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    )}

                    {/* 开关按钮 */}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onToggleProvider(provider.id);
                      }}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none ${
                        provider.enable
                          ? 'bg-blue-600'
                          : 'bg-gray-200 dark:bg-gray-700'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          provider.enable ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

// 服务商列表组件（保留原有的，用于兼容）
const ProviderList: React.FC = () => {
  const { providers, selectedProviderId, setSelectedProvider, toggleProvider } = useConfigStore();

  return (
    <div className="w-80 border-r border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 flex-shrink-0 flex flex-col">
      <div className="flex-1 overflow-y-auto">
        <div className="p-4">
          <div className="space-y-1">
            {providers.map((provider) => (
              <div
                key={provider.id}
                className={`flex items-center justify-between py-2 px-3 rounded-lg cursor-pointer transition-colors ${
                  selectedProviderId === provider.id
                    ? 'bg-blue-50 dark:bg-blue-900/20'
                    : 'hover:bg-gray-50 dark:hover:bg-gray-800'
                }`}
                onClick={() => setSelectedProvider(provider.id)}
              >
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    {/* 服务商图标 */}
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${getProviderIconStyle(provider.id)}`}>
                      <span className="text-xs font-medium text-white">
                        {getProviderIcon(provider.id)}
                      </span>
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {provider.name}
                    </p>
                  </div>
                </div>

                {/* 开关按钮 */}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleProvider(provider.id);
                  }}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none ${
                    provider.enabled
                      ? 'bg-blue-600'
                      : 'bg-gray-200 dark:bg-gray-700'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      provider.enabled ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// 增强的提供商详情组件接口
interface EnhancedProviderDetailProps {
  providers: LLMProvider[];
  selectedProviderId: string | null;
  providerModels: Map<string, ModelMeta[]>;
  isLoadingModels: Set<string>;
  validationResults: Map<string, ProviderCheckResult>;
  onUpdateProvider: (providerId: string, updates: Partial<LLMProvider>) => void;
  onFetchModels: (providerId: string) => Promise<void>;
  onValidateProvider: (providerId: string) => Promise<ProviderCheckResult | undefined>;
}

// 增强的提供商详情组件
const EnhancedProviderDetail: React.FC<EnhancedProviderDetailProps> = ({
  providers,
  selectedProviderId,
  providerModels,
  isLoadingModels,
  validationResults,
  onUpdateProvider,
  onFetchModels,
  onValidateProvider
}) => {
  const [isValidating, setIsValidating] = useState(false);
  const [showApiKey, setShowApiKey] = useState(false);
  const [modelStates, setModelStates] = useState<Map<string, boolean>>(new Map());

  const selectedProvider = providers.find(p => p.id === selectedProviderId);
  const models = selectedProviderId ? providerModels.get(selectedProviderId) || [] : [];
  const validationResult = selectedProviderId ? validationResults.get(selectedProviderId) : undefined;
  const isLoading = selectedProviderId ? isLoadingModels.has(selectedProviderId) : false;

  // 处理 API Key 验证
  const handleValidateApiKey = async () => {
    if (!selectedProvider || !selectedProvider.apiKey.trim()) {
      alert('请先输入API Key');
      return;
    }

    setIsValidating(true);
    try {
      const result = await onValidateProvider(selectedProvider.id);
      if (result?.isOk) {
        alert('API Key验证成功！');
        // 验证成功后自动获取模型列表
        await onFetchModels(selectedProvider.id);
      } else {
        alert(`API Key验证失败：${result?.errorMsg || '未知错误'}`);
      }
    } catch (error) {
      alert('验证过程中出现错误');
    } finally {
      setIsValidating(false);
    }
  };

  // 处理获取 API Key
  const handleGetApiKey = () => {
    if (!selectedProvider?.websites?.apiKey) {
      alert('请访问该服务商官网获取API Key');
      return;
    }
    window.open(selectedProvider.websites.apiKey, '_blank');
  };

  // 切换模型启用状态
  const toggleModelEnabled = (modelId: string) => {
    setModelStates(prev => {
      const newMap = new Map(prev);
      newMap.set(modelId, !newMap.get(modelId));
      return newMap;
    });
  };

  // 启用所有模型
  const enableAllModels = () => {
    const newMap = new Map<string, boolean>();
    models.forEach(model => {
      newMap.set(model.id, true);
    });
    setModelStates(newMap);
  };

  // 禁用所有模型
  const disableAllModels = () => {
    const newMap = new Map<string, boolean>();
    models.forEach(model => {
      newMap.set(model.id, false);
    });
    setModelStates(newMap);
  };

  if (!selectedProvider) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <p className="text-gray-500 dark:text-gray-400">请选择一个服务商</p>
      </div>
    );
  }

  return (
    <div className="flex-1 bg-white dark:bg-gray-900 overflow-auto">
      <div className="p-6">
        <div className="max-w-2xl">
          {/* 服务商标题 */}
          <div className="flex items-center space-x-3 mb-6">
            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${getProviderIconStyle(selectedProvider.id)}`}>
              <span className="text-sm font-medium text-white">
                {getProviderIcon(selectedProvider.id)}
              </span>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              {selectedProvider.name}
            </h2>
          </div>

          {/* API URL配置 */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              API URL
            </label>
            <input
              type="text"
              value={selectedProvider.baseUrl}
              onChange={(e) => onUpdateProvider(selectedProvider.id, { baseUrl: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
              placeholder="请输入API URL"
            />
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              默认: {selectedProvider.websites?.defaultBaseUrl || 'https://api.openai.com/v1'}
            </p>
          </div>

          {/* API Key配置 */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              API Key
            </label>
            <div className="relative">
              <input
                type={showApiKey ? "text" : "password"}
                value={selectedProvider.apiKey}
                onChange={(e) => onUpdateProvider(selectedProvider.id, { apiKey: e.target.value })}
                className="w-full px-3 py-2 pr-20 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:text-white"
                placeholder="请输入API Key"
              />
              <button
                type="button"
                onClick={() => setShowApiKey(!showApiKey)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
              >
                {showApiKey ? <EyeSlashIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
              </button>
            </div>

            {/* API Key操作按钮 */}
            <div className="mt-3 flex space-x-3">
              <button
                onClick={handleValidateApiKey}
                disabled={isValidating || !selectedProvider.apiKey.trim()}
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isValidating ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    验证中...
                  </>
                ) : (
                  '验证密钥'
                )}
              </button>

              <button
                onClick={handleGetApiKey}
                className="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                如何获取
              </button>
            </div>

            {/* 验证状态显示 */}
            {validationResult && (
              <div className={`mt-2 flex items-center text-sm ${
                validationResult.isOk
                  ? 'text-green-600 dark:text-green-400'
                  : 'text-red-600 dark:text-red-400'
              }`}>
                <span className={`inline-block w-2 h-2 rounded-full mr-2 ${
                  validationResult.isOk ? 'bg-green-500' : 'bg-red-500'
                }`}></span>
                {validationResult.isOk ? 'API Key已验证' : `验证失败: ${validationResult.errorMsg}`}
              </div>
            )}
          </div>

          {/* 模型列表 */}
          <EnhancedModelList
            models={models}
            isLoading={isLoading}
            modelStates={modelStates}
            onToggleModel={toggleModelEnabled}
            onEnableAll={enableAllModels}
            onDisableAll={disableAllModels}
            onFetchModels={() => onFetchModels(selectedProvider.id)}
          />
        </div>
      </div>
    </div>
  );
};

// 增强的模型列表组件接口
interface EnhancedModelListProps {
  models: ModelMeta[];
  isLoading: boolean;
  modelStates: Map<string, boolean>;
  onToggleModel: (modelId: string) => void;
  onEnableAll: () => void;
  onDisableAll: () => void;
  onFetchModels: () => Promise<void>;
}

// 增强的模型列表组件
const EnhancedModelList: React.FC<EnhancedModelListProps> = ({
  models,
  isLoading,
  modelStates,
  onToggleModel,
  onEnableAll,
  onDisableAll,
  onFetchModels
}) => {
  const [showCustomModelForm, setShowCustomModelForm] = useState(false);
  const [newModel, setNewModel] = useState({
    id: '',
    name: '',
    contextLength: 4096,
    maxTokens: 2048,
    description: ''
  });

  const handleAddCustomModel = () => {
    if (newModel.id && newModel.name) {
      // 这里可以添加自定义模型的逻辑
      console.log('Adding custom model:', newModel);
      setNewModel({
        id: '',
        name: '',
        contextLength: 4096,
        maxTokens: 2048,
        description: ''
      });
      setShowCustomModelForm(false);
    }
  };

  return (
    <div className="mt-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">模型列表</h3>
        <div className="flex space-x-2">
          <button
            onClick={onFetchModels}
            disabled={isLoading}
            className="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {isLoading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                加载中...
              </>
            ) : (
              '刷新模型'
            )}
          </button>
          <button
            onClick={() => setShowCustomModelForm(!showCustomModelForm)}
            className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <PlusIcon className="h-4 w-4 mr-1" />
            添加模型
          </button>
        </div>
      </div>

      {/* 添加自定义模型表单 */}
      {showCustomModelForm && (
        <div className="mb-4 p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">添加自定义模型</h4>
          <div className="grid grid-cols-2 gap-3">
            <input
              type="text"
              placeholder="模型ID"
              value={newModel.id}
              onChange={(e) => setNewModel(prev => ({ ...prev, id: e.target.value }))}
              className="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
            <input
              type="text"
              placeholder="模型名称"
              value={newModel.name}
              onChange={(e) => setNewModel(prev => ({ ...prev, name: e.target.value }))}
              className="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
            <input
              type="number"
              placeholder="上下文长度"
              value={newModel.contextLength}
              onChange={(e) => setNewModel(prev => ({ ...prev, contextLength: parseInt(e.target.value) || 4096 }))}
              className="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
            <input
              type="number"
              placeholder="最大输出"
              value={newModel.maxTokens}
              onChange={(e) => setNewModel(prev => ({ ...prev, maxTokens: parseInt(e.target.value) || 2048 }))}
              className="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
          <input
            type="text"
            placeholder="描述（可选）"
            value={newModel.description}
            onChange={(e) => setNewModel(prev => ({ ...prev, description: e.target.value }))}
            className="mt-3 w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
          />
          <div className="flex space-x-2 mt-3">
            <button
              onClick={handleAddCustomModel}
              className="flex-1 px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              添加
            </button>
            <button
              onClick={() => setShowCustomModelForm(false)}
              className="flex-1 px-3 py-2 text-sm bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              取消
            </button>
          </div>
        </div>
      )}

      {/* 模型列表 */}
      {models.length === 0 ? (
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          {isLoading ? '正在加载模型...' : '暂无可用模型，请先验证API Key并刷新模型列表'}
        </div>
      ) : (
        <>
          {/* 批量操作按钮 */}
          <div className="flex space-x-2 mb-4">
            <button
              onClick={onEnableAll}
              className="px-3 py-1.5 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              全部启用
            </button>
            <button
              onClick={onDisableAll}
              className="px-3 py-1.5 text-sm bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              全部禁用
            </button>
          </div>

          {/* 模型卡片列表 */}
          <div className="space-y-2">
            {models.map((model) => {
              const isEnabled = modelStates.get(model.id) ?? model.enabled ?? false;

              return (
                <div
                  key={model.id}
                  className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800"
                >
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {model.name}
                      </h4>
                      {model.isCustom && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                          自定义
                        </span>
                      )}
                      {model.vision && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                          视觉
                        </span>
                      )}
                      {model.functionCall && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                          函数调用
                        </span>
                      )}
                    </div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {model.description || `上下文: ${model.contextLength?.toLocaleString() || 'N/A'} | 最大输出: ${model.maxTokens?.toLocaleString() || 'N/A'}`}
                    </p>
                  </div>

                  <div className="flex items-center space-x-2">
                    {/* 删除按钮（仅自定义模型） */}
                    {model.isCustom && (
                      <button
                        onClick={() => {
                          // 这里可以添加删除自定义模型的逻辑
                          console.log('Deleting custom model:', model.id);
                        }}
                        className="p-1 text-gray-400 hover:text-red-600 dark:hover:text-red-400"
                        title="删除自定义模型"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    )}

                    {/* 开关按钮 */}
                    <button
                      onClick={() => onToggleModel(model.id)}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none ${
                        isEnabled
                          ? 'bg-blue-600'
                          : 'bg-gray-200 dark:bg-gray-700'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          isEnabled ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        </>
      )}
    </div>
  );
};

// 模型列表组件（保留原有的，用于兼容）
interface ModelListProps {
  provider: any;
}

const ModelList: React.FC<ModelListProps> = ({ provider }) => {
  const { toggleProviderModel, enableAllModels, disableAllModels } = useConfigStore();

  if (!provider.models || provider.models.length === 0) {
    return (
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">模型列表</h3>
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          <p>暂无可用模型</p>
          <p className="text-sm mt-1">请确保API Key配置正确后重新加载</p>
        </div>
      </div>
    );
  }

  const enabledModelsCount = provider.models.filter((model: any) => model.enabled).length;
  const totalModelsCount = provider.models.length;

  return (
    <div className="mb-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">模型列表</h3>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {enabledModelsCount}/{totalModelsCount} 模型已启用
          </span>
        </div>
      </div>

      {/* 模型操作按钮 */}
      <div className="flex items-center space-x-2 mb-4">
        <button
          onClick={() => enableAllModels(provider.id)}
          className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-900 dark:text-blue-300 dark:hover:bg-blue-800"
        >
          启用全部
        </button>
        <button
          onClick={() => disableAllModels(provider.id)}
          className="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          禁用全部
        </button>
      </div>

      {/* 模型列表 */}
      <div className="space-y-2">
        {provider.models
          .sort((a: any, b: any) => {
            // 启用的模型排在前面，禁用的排在后面
            if (a.enabled && !b.enabled) return -1;
            if (!a.enabled && b.enabled) return 1;
            return 0;
          })
          .map((model: any) => (
            <div
              key={model.id}
              className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {model.name}
                  </p>
                </div>
              </div>

              {/* 模型开关 */}
              <button
                onClick={() => toggleProviderModel(provider.id, model.id)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none ${
                  model.enabled
                    ? 'bg-blue-600'
                    : 'bg-gray-200 dark:bg-gray-700'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    model.enabled ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          ))}
      </div>
    </div>
  );
};

export default ConfigurationTabContent;